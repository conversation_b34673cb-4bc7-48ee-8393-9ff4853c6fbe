# Matching Networks 实现

这是一个用于小样本分类任务的Matching Networks实现，与现有的PrototypicalNetwork代码兼容。

## 文件结构

- `attention_modules.py` - 注意力机制模块
- `matching_network.py` - Matching Network主要实现
- `matching_network_test.py` - 单独训练和测试Matching Network
- `compare_networks.py` - 对比PrototypicalNetwork和MatchingNetwork的性能

## 主要特性

### Matching Network特点
1. **注意力机制**: 使用余弦相似度或点积注意力
2. **Full Context Embedding (FCE)**: 可选的双向LSTM处理支持集
3. **兼容性**: 与现有PrototypicalNetwork使用相同的数据格式和网络结构
4. **灵活配置**: 支持多种注意力类型和网络参数

### 网络结构
- 特征提取: Conv1D + 残差块 + Transformer编码器
- 嵌入网络: 多层全连接网络
- 注意力机制: 余弦相似度或点积注意力
- FCE: 可选的双向LSTM上下文编码

## 使用方法

### 1. 单独训练Matching Network
```bash
python matching_network_test.py
```

### 2. 对比两种网络性能
```bash
python compare_networks.py
```

### 3. 在代码中使用
```python
from matching_network import MatchingNetwork, matching_loss, process_batch_matching

# 创建模型
model = MatchingNetwork(
    input_size=8,
    hidden_size=256,
    output_size=64,
    use_fce=True,
    attention_type='cosine'
)

# 训练
predictions, query_labels = process_batch_matching(
    model, batch, device, num_classes, num_support
)
loss = matching_loss(predictions, query_labels)
```

## 参数说明

### MatchingNetwork参数
- `input_size`: 输入特征维度 (默认: 8)
- `hidden_size`: 隐藏层大小 (默认: 256)
- `output_size`: 嵌入空间大小 (默认: 64)
- `nhead`: Transformer注意力头数 (默认: 8)
- `num_layers`: Transformer层数 (默认: 2)
- `seq_len`: 序列长度 (默认: 8)
- `use_fce`: 是否使用Full Context Embedding (默认: True)
- `attention_type`: 注意力类型 ('cosine' 或 'dot_product')

### 训练参数
- `num_classes`: 类别数 (默认: 10)
- `num_support`: 每类支持集样本数 (默认: 10)
- `num_query`: 每类查询集样本数 (默认: 15)
- `learning_rate`: 学习率 (默认: 0.001)

## 算法原理

### Matching Networks
Matching Networks通过注意力机制直接比较查询样本与支持集样本的相似度进行分类：

1. **特征提取**: 将输入数据转换为嵌入向量
2. **上下文编码**: 使用FCE对支持集进行上下文编码
3. **注意力计算**: 计算查询样本与每个支持样本的注意力权重
4. **分类预测**: 基于注意力权重对每个类别进行加权投票

### 与Prototypical Networks的区别
- **Prototypical**: 计算类别原型，基于距离分类
- **Matching**: 直接计算样本间注意力，基于相似度分类

## 输出文件

训练完成后会生成以下文件：
- `best_matching_network.pth` - 最佳模型权重
- `final_matching_network.pth` - 最终模型权重
- `matching_networks_training_log.txt` - 训练日志
- `matching_networks_confusion_matrix.png` - 混淆矩阵图
- `networks_comparison.png` - 网络性能对比图 (使用compare_networks.py时)

## 性能对比

使用`compare_networks.py`可以直接对比两种网络的：
- 训练损失曲线
- 测试准确率曲线
- 训练时间
- 最终性能指标

## 注意事项

1. 确保已安装所需依赖包
2. 数据文件`traindata_5dB.mat`需要在当前目录
3. 建议使用GPU进行训练以提高速度
4. 可以根据数据特点调整网络参数和注意力类型

## 扩展功能

- 支持不同的注意力机制
- 可配置的网络结构
- 兼容现有的数据处理流程
- 易于与其他小样本学习方法对比
