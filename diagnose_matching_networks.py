"""
诊断Matching Networks性能问题的脚本
"""

import torch
import torch.nn.functional as F
import numpy as np
from MatchingNetworks import MatchingNetwork
from Test import PrototypicalNetwork, compute_prototypes
import matplotlib.pyplot as plt

def compare_embeddings():
    """比较两个网络的嵌入质量"""
    print("🔍 比较嵌入质量...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 参数
    input_size = 8
    hidden_size = 256
    output_size = 64
    num_classes = 10
    num_samples = 100
    
    # 创建模型
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    proto_net = PrototypicalNetwork(input_size, hidden_size, output_size)
    
    matching_net.to(device)
    proto_net.to(device)
    
    # 创建测试数据
    test_data = torch.randn(num_samples, 8).to(device)
    test_labels = torch.randint(0, num_classes, (num_samples,)).to(device)
    
    with torch.no_grad():
        # 获取嵌入
        matching_embeddings = matching_net.encode(test_data)
        proto_embeddings = proto_net(test_data)
        
        print(f"Matching Networks嵌入形状: {matching_embeddings.shape}")
        print(f"Prototypical Network嵌入形状: {proto_embeddings.shape}")
        
        # 检查嵌入的统计特性
        print(f"\nMatching Networks嵌入统计:")
        print(f"  均值: {matching_embeddings.mean().item():.4f}")
        print(f"  标准差: {matching_embeddings.std().item():.4f}")
        print(f"  最小值: {matching_embeddings.min().item():.4f}")
        print(f"  最大值: {matching_embeddings.max().item():.4f}")
        
        print(f"\nPrototypical Network嵌入统计:")
        print(f"  均值: {proto_embeddings.mean().item():.4f}")
        print(f"  标准差: {proto_embeddings.std().item():.4f}")
        print(f"  最小值: {proto_embeddings.min().item():.4f}")
        print(f"  最大值: {proto_embeddings.max().item():.4f}")
        
        # 计算类内和类间距离
        matching_intra, matching_inter = compute_distances(matching_embeddings, test_labels, num_classes)
        proto_intra, proto_inter = compute_distances(proto_embeddings, test_labels, num_classes)
        
        print(f"\n距离分析:")
        print(f"Matching Networks - 类内距离: {matching_intra:.4f}, 类间距离: {matching_inter:.4f}, 比值: {matching_inter/matching_intra:.4f}")
        print(f"Prototypical Network - 类内距离: {proto_intra:.4f}, 类间距离: {proto_inter:.4f}, 比值: {proto_inter/proto_intra:.4f}")


def compute_distances(embeddings, labels, num_classes):
    """计算类内和类间平均距离"""
    intra_distances = []
    inter_distances = []
    
    for i in range(num_classes):
        class_mask = (labels == i)
        if class_mask.sum() < 2:
            continue
            
        class_embeddings = embeddings[class_mask]
        
        # 类内距离
        if len(class_embeddings) > 1:
            intra_dist = torch.cdist(class_embeddings, class_embeddings)
            # 排除对角线（自己与自己的距离）
            mask = torch.eye(len(class_embeddings), device=embeddings.device).bool()
            intra_dist = intra_dist[~mask]
            intra_distances.extend(intra_dist.cpu().numpy())
        
        # 类间距离
        for j in range(i+1, num_classes):
            other_mask = (labels == j)
            if other_mask.sum() == 0:
                continue
            other_embeddings = embeddings[other_mask]
            inter_dist = torch.cdist(class_embeddings, other_embeddings)
            inter_distances.extend(inter_dist.flatten().cpu().numpy())
    
    return np.mean(intra_distances), np.mean(inter_distances)


def test_attention_mechanism():
    """测试注意力机制是否工作正常"""
    print("\n🔍 测试注意力机制...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 参数
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # 创建模型
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    
    # 创建测试数据：同一类的样本应该相似
    num_support = 5
    support_data = torch.randn(num_support, 8).to(device)
    
    # 添加一些噪声使样本略有不同
    for i in range(1, num_support):
        support_data[i] = support_data[0] + 0.1 * torch.randn(8).to(device)
    
    with torch.no_grad():
        # 获取嵌入
        support_embeddings = matching_net.encode(support_data)
        
        # 测试注意力
        class_prototype, attention_weights = matching_net.support_attention(support_embeddings)
        
        print(f"支持集嵌入形状: {support_embeddings.shape}")
        print(f"类原型形状: {class_prototype.shape}")
        print(f"注意力权重: {attention_weights.squeeze().cpu().numpy()}")
        print(f"注意力权重和: {attention_weights.sum().item():.4f}")
        
        # 检查注意力权重是否合理分布
        weights = attention_weights.squeeze().cpu().numpy()
        print(f"注意力权重标准差: {weights.std():.4f}")
        print(f"最大权重: {weights.max():.4f}, 最小权重: {weights.min():.4f}")


def test_temperature_scaling():
    """测试温度缩放参数"""
    print("\n🔍 测试温度缩放...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 参数
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # 创建模型
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    
    print(f"初始温度参数: {matching_net.temperature.item():.4f}")
    
    # 测试不同温度值的影响
    query = torch.randn(1, 64).to(device)
    prototype = torch.randn(1, 64).to(device)
    
    # 归一化
    query = F.normalize(query, p=2, dim=1)
    prototype = F.normalize(prototype, p=2, dim=1)
    
    cosine_sim = F.cosine_similarity(query, prototype, dim=1)
    
    temperatures = [1.0, 5.0, 10.0, 20.0]
    print(f"余弦相似度: {cosine_sim.item():.4f}")
    
    for temp in temperatures:
        scaled_logit = cosine_sim * temp
        probability = torch.softmax(scaled_logit.unsqueeze(0), dim=1)
        print(f"温度 {temp}: 缩放后logit = {scaled_logit.item():.4f}, 概率 = {probability.item():.4f}")


def diagnose_gradient_flow():
    """诊断梯度流问题"""
    print("\n🔍 诊断梯度流...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 参数
    input_size = 8
    hidden_size = 256
    output_size = 64
    num_classes = 10
    num_support = 5
    num_query = 5
    
    # 创建模型
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    
    # 创建测试数据
    batch_size = num_classes * (num_support + num_query)
    test_data = torch.randn(batch_size, 8).to(device)
    test_labels = torch.arange(num_classes).repeat_interleave(num_support + num_query).to(device)
    
    # 分割数据
    support_data = test_data[:num_classes * num_support]
    support_labels = test_labels[:num_classes * num_support]
    query_data = test_data[num_classes * num_support:]
    query_labels = test_labels[num_classes * num_support:]
    
    # 前向传播
    logits = matching_net(support_data, support_labels, query_data, num_classes, num_support)
    loss = F.cross_entropy(logits, query_labels)
    
    print(f"损失值: {loss.item():.4f}")
    print(f"Logits形状: {logits.shape}")
    print(f"Logits统计: 均值={logits.mean().item():.4f}, 标准差={logits.std().item():.4f}")
    
    # 反向传播
    loss.backward()
    
    # 检查梯度
    total_norm = 0
    param_count = 0
    for name, param in matching_net.named_parameters():
        if param.grad is not None:
            param_norm = param.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
            param_count += 1
            if 'temperature' in name or 'attention' in name:
                print(f"{name}: 梯度范数 = {param_norm:.6f}")
    
    total_norm = total_norm ** (1. / 2)
    print(f"总梯度范数: {total_norm:.6f}")
    print(f"有梯度的参数数量: {param_count}")


def main():
    print("=" * 60)
    print("MATCHING NETWORKS 性能诊断")
    print("=" * 60)
    
    # 1. 比较嵌入质量
    compare_embeddings()
    
    # 2. 测试注意力机制
    test_attention_mechanism()
    
    # 3. 测试温度缩放
    test_temperature_scaling()
    
    # 4. 诊断梯度流
    diagnose_gradient_flow()
    
    print("\n" + "=" * 60)
    print("诊断完成！")
    print("=" * 60)
    
    print("\n💡 改进建议:")
    print("1. 如果类内距离过大，考虑增加正则化")
    print("2. 如果注意力权重分布不均，调整注意力网络结构")
    print("3. 如果温度参数不合适，调整初始值或学习率")
    print("4. 如果梯度范数过小，增加学习率；过大则减少学习率")


if __name__ == '__main__':
    main()
