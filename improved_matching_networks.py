import random
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy.io import loadmat
from sklearn.model_selection import train_test_split
from torch import optim
from torch.utils.data import DataLoader
from data import CustomDataset
import matplotlib.pyplot as plt
from tqdm import tqdm

# Import the improved Matching Networks
from MatchingNetworks import MatchingNetwork, matching_loss, process_batch_matching, evaluate_matching_network

def set_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


class EarlyStopping:
    """Early stopping to prevent overfitting"""
    def __init__(self, patience=20, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_accuracy = 0
        
    def __call__(self, accuracy):
        if accuracy > self.best_accuracy + self.min_delta:
            self.best_accuracy = accuracy
            self.counter = 0
            return False
        else:
            self.counter += 1
            return self.counter >= self.patience


def train_improved_matching_networks():
    """Train Matching Networks with improved settings"""
    seed = 42
    set_seed(seed)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据集参数
    num_classes = 10
    num_support = 10
    num_query = 15
    
    # Load data
    try:
        dictdata = loadmat("traindata_5dB.mat")
        dataload = dictdata['traindata']
        print(f"Data loaded successfully. Shape: {dataload.shape}")
    except FileNotFoundError:
        print("traindata_5dB.mat not found. Please make sure the file exists.")
        return None, [], []
    
    # 网络参数
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # 创建数据加载器
    dataset = CustomDataset(dataload, noisy=False)
    train_dataset, test_dataset = train_test_split(dataset, test_size=0.9, random_state=42)
    
    batch_size = num_classes * (num_support + num_query)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=True)
    
    print(f"Train batches: {len(train_loader)}, Test batches: {len(test_loader)}")
    
    # 创建改进的Matching Network
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    
    # 使用更好的优化器设置
    optimizer = optim.AdamW(matching_net.parameters(), lr=0.001, weight_decay=1e-4)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=10, verbose=True
    )
    
    # 早停机制
    early_stopping = EarlyStopping(patience=30, min_delta=0.5)
    
    # 训练记录
    train_losses = []
    test_accuracies = []
    best_accuracy = 0
    
    print("Starting training with improved Matching Networks...")
    
    for epoch in range(300):
        # Training phase
        matching_net.train()
        epoch_loss = 0
        batch_count = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch + 1}'):
            try:
                # Process batch
                logits, query_labels = process_batch_matching(
                    matching_net, batch, device, num_classes, num_support
                )
                
                # 计算损失
                loss = matching_loss(logits, query_labels)
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                
                # 梯度裁剪防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(matching_net.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
                
            except Exception as e:
                print(f"Error in batch processing: {e}")
                continue
        
        if batch_count == 0:
            print("No valid batches processed!")
            break
            
        avg_loss = epoch_loss / batch_count
        train_losses.append(avg_loss)
        
        # Evaluation phase
        try:
            test_accuracy = evaluate_matching_network(
                matching_net, test_loader, device, num_classes, num_support
            )
            test_accuracies.append(test_accuracy)
            
            # 学习率调度
            scheduler.step(test_accuracy)
            
            # 保存最佳模型
            if test_accuracy > best_accuracy:
                best_accuracy = test_accuracy
                torch.save(matching_net.state_dict(), 'best_matching_network.pth')
                print(f"✅ New best model saved! Accuracy: {best_accuracy:.2f}%")
            
            print(f'Epoch {epoch + 1}: Loss = {avg_loss:.4f}, Accuracy = {test_accuracy:.2f}%, Best = {best_accuracy:.2f}%')
            
            # 早停检查
            if early_stopping(test_accuracy):
                print(f"Early stopping triggered at epoch {epoch + 1}")
                break
                
        except Exception as e:
            print(f"Error in evaluation: {e}")
            continue
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses)
    plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(test_accuracies)
    plt.title('Test Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('improved_matching_networks_training.png')
    plt.show()
    
    print(f"\n🎉 Training completed!")
    print(f"Best accuracy achieved: {best_accuracy:.2f}%")
    if test_accuracies:
        print(f"Final accuracy: {test_accuracies[-1]:.2f}%")
    
    return matching_net, train_losses, test_accuracies


def debug_data_flow():
    """Debug function to check data flow"""
    print("Debugging data flow...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Parameters
    num_classes = 10
    num_support = 10
    num_query = 15
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # Create dummy data
    batch_size = num_classes * (num_support + num_query)
    dummy_data = torch.randn(batch_size, 8)
    dummy_labels = torch.randint(0, num_classes, (batch_size,))
    
    # Ensure each class has exactly num_support + num_query samples
    for i in range(num_classes):
        start_idx = i * (num_support + num_query)
        end_idx = start_idx + (num_support + num_query)
        dummy_labels[start_idx:end_idx] = i
    
    batch = (dummy_data, dummy_labels)
    
    print(f"Batch data shape: {dummy_data.shape}")
    print(f"Batch labels shape: {dummy_labels.shape}")
    print(f"Label distribution: {torch.bincount(dummy_labels)}")
    
    # Test model
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    
    try:
        logits, query_labels = process_batch_matching(
            matching_net, batch, device, num_classes, num_support
        )
        
        print(f"✅ Forward pass successful!")
        print(f"Logits shape: {logits.shape}")
        print(f"Query labels shape: {query_labels.shape}")
        print(f"Expected query labels: {num_classes * num_query}")
        
        loss = matching_loss(logits, query_labels)
        print(f"Loss: {loss.item():.4f}")
        
        # Test prediction
        _, predicted = torch.max(logits, dim=1)
        accuracy = (predicted == query_labels).float().mean().item() * 100
        print(f"Random accuracy: {accuracy:.2f}%")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("=" * 60)
    print("IMPROVED MATCHING NETWORKS TRAINING")
    print("=" * 60)
    
    # First debug the data flow
    debug_data_flow()
    
    print("\n" + "=" * 60)
    print("STARTING ACTUAL TRAINING")
    print("=" * 60)
    
    # Then train the model
    model, losses, accuracies = train_improved_matching_networks()
